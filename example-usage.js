const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function demonstrateWorkflow() {
  console.log('🚀 Webhook Data Forwarder with MongoDB - Complete Workflow Demo\n');

  try {
    // Step 1: Create an Account
    console.log('📝 Step 1: Creating an account...');
    const accountResponse = await axios.post(`${BASE_URL}/api/accounts`, {
      email: '<EMAIL>',
      accountName: 'Demo Company',
      website: 'https://democompany.com'
    });
    
    const account = accountResponse.data.data;
    console.log(`✅ Account created: ${account.accountName} (${account.accountId})`);
    console.log(`🔑 App Secret Token: ${account.appSecretToken}\n`);

    // Step 2: Create Multiple Destinations
    console.log('🎯 Step 2: Creating webhook destinations...');
    
    // Destination 1: POST endpoint
    const dest1Response = await axios.post(`${BASE_URL}/api/destinations`, {
      accountId: account.accountId,
      url: 'https://httpbin.org/post',
      httpMethod: 'POST',
      headers: {
        'Authorization': 'Bearer demo-token-1',
        'X-Source': 'webhook-forwarder',
        'Content-Type': 'application/json',
        'APP_ID': '1234APPID1234',
        'APP_SECRET': 'enwdj3bshwer43bjhjs9ereuinkjcnsiurew8s',
        'ACTION': 'user.update'
      }
    });
    console.log(`✅ POST destination created: ${dest1Response.data.data.destinationId}`);

    // Destination 2: GET endpoint
    const dest2Response = await axios.post(`${BASE_URL}/api/destinations`, {
      accountId: account.accountId,
      url: 'https://httpbin.org/get',
      httpMethod: 'GET',
      headers: {
        'Authorization': 'Bearer demo-token-2',
        'X-Source': 'webhook-forwarder'
      }
    });
    console.log(`✅ GET destination created: ${dest2Response.data.data.destinationId}\n`);

    // Step 3: List Destinations for Account
    console.log('📋 Step 3: Listing destinations for account...');
    const destinationsResponse = await axios.get(`${BASE_URL}/api/destinations/account/${account.accountId}`);
    console.log(`✅ Found ${destinationsResponse.data.count} destinations configured\n`);

    // Step 4: Send Sample Data
    console.log('📤 Step 4: Sending sample data...');
    const sampleData = {
      event: 'user_registration',
      timestamp: new Date().toISOString(),
      user: {
        id: 12345,
        name: 'John Doe',
        email: '<EMAIL>',
        plan: 'premium'
      },
      metadata: {
        source: 'web_app',
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    };

    const dataResponse = await axios.post(`${BASE_URL}/server/incoming_data`, sampleData, {
      headers: {
        'CL-X-TOKEN': account.appSecretToken,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Data forwarded successfully!');
    console.log(`📊 Results:`);
    console.log(`   - Total destinations: ${dataResponse.data.totalDestinations}`);
    console.log(`   - Successful sends: ${dataResponse.data.successfulSends}`);
    console.log(`   - Failed sends: ${dataResponse.data.failedSends}`);
    
    if (dataResponse.data.results.successful.length > 0) {
      console.log('\n✅ Successful deliveries:');
      dataResponse.data.results.successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.method} ${result.url} - Status: ${result.status}`);
      });
    }

    if (dataResponse.data.results.failed.length > 0) {
      console.log('\n❌ Failed deliveries:');
      dataResponse.data.results.failed.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.method} ${result.url} - Error: ${result.error}`);
      });
    }

    // Step 5: Update Account
    console.log('\n🔄 Step 5: Updating account information...');
    const updateResponse = await axios.put(`${BASE_URL}/api/accounts/${account.accountId}`, {
      accountName: 'Updated Demo Company',
      website: 'https://updated-democompany.com'
    });
    console.log(`✅ Account updated: ${updateResponse.data.data.accountName}`);

    // Step 6: Get Account Statistics
    console.log('\n📈 Step 6: Getting updated destinations...');
    const updatedDestinations = await axios.get(`${BASE_URL}/api/destinations/account/${account.accountId}`);
    console.log('✅ Destination statistics:');
    updatedDestinations.data.data.forEach((dest, index) => {
      console.log(`   ${index + 1}. ${dest.url} - Success: ${dest.successCount}, Failures: ${dest.failureCount}`);
    });

    console.log('\n🎉 Demo completed successfully!');
    console.log('\n📝 Summary:');
    console.log(`   - Account ID: ${account.accountId}`);
    console.log(`   - Email: ${account.email}`);
    console.log(`   - Destinations: ${destinationsResponse.data.count}`);
    console.log(`   - Data forwarded to: ${dataResponse.data.successfulSends} endpoints`);
    console.log(`   - Database: MongoDB`);

  } catch (error) {
    console.error('❌ Demo failed:', error.response?.data || error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Make sure the server is running on port 3000');
      console.error('   Run: npm start');
    }
  }
}

// Run the demo
if (require.main === module) {
  demonstrateWorkflow();
}

module.exports = { demonstrateWorkflow };
